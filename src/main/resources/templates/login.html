<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - File Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 450px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #64ffda, transparent);
        }

        .logo {
            font-size: 3rem;
            background: linear-gradient(135deg, #64ffda 0%, #1de9b6 50%, #00bcd4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(100, 255, 218, 0.3);
        }

        h1 {
            color: #e2e8f0;
            margin-bottom: 0.5rem;
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #64ffda 0%, #1de9b6 50%, #00bcd4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #94a3b8;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            font-weight: 300;
        }

        .google-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: rgba(100, 255, 218, 0.1);
            color: #64ffda;
            padding: 14px 28px;
            border: 1px solid rgba(100, 255, 218, 0.3);
            border-radius: 12px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .google-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .google-btn:hover::before {
            left: 100%;
        }

        .google-btn:hover {
            background: rgba(100, 255, 218, 0.15);
            border-color: #64ffda;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(100, 255, 218, 0.2);
            color: #ffffff;
        }

        .google-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            padding: 2px;
            position: relative;
            z-index: 1;
        }

        .alert {
            padding: 14px 18px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.15);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.15);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .features {
            margin-top: 2.5rem;
            text-align: left;
            background: rgba(255, 255, 255, 0.03);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .features h3 {
            color: #e2e8f0;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .features ul {
            list-style: none;
            color: #94a3b8;
        }

        .features li {
            margin-bottom: 0.8rem;
            padding-left: 2rem;
            position: relative;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #64ffda;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .footer {
            margin-top: 2.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #64748b;
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        .login-container {
            animation: fadeInUp 0.8s ease-out;
        }

        .logo {
            animation: pulse 3s ease-in-out infinite;
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 1rem;
            }

            .logo {
                font-size: 2.5rem;
            }

            h1 {
                font-size: 1.6rem;
            }

            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">📁</div>
        <h1>File Manager</h1>
        <p class="subtitle">Secure cloud file storage and management</p>

        <!-- Error message -->
        <div th:if="${param.error}" class="alert alert-error">
            Authentication failed. Please try again.
        </div>

        <!-- Logout success message -->
        <div th:if="${param.logout}" class="alert alert-success" id="logout-message">
            You have been successfully logged out.
        </div>

        <!-- Google OAuth2 Login Button -->
        <a href="/oauth2/authorization/google" class="google-btn">
            <svg class="google-icon" viewBox="0 0 24 24">
                <path fill="#64ffda" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#1de9b6" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#00bcd4" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#64ffda" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
        </a>

        <div class="features">
            <h3>Features:</h3>
            <ul>
                <li>Secure file upload and storage</li>
                <li>File search and organization</li>
                <li>Download or Delete files</li>
                <li>Google account integration</li>
            </ul>
        </div>

        <div class="footer">
            Powered by Spring Boot & AWS S3
        </div>
    </div>
</body>
</html>
